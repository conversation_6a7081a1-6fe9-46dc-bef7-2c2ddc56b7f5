import { DashboardStats } from '@/types';
import { apiService } from './api';

class DashboardService {
  async getStats() {
    const response = await apiService.get('/dashboard/stats');
    return response;
  }

  async getRecentActivity() {
    const response = await apiService.get('/dashboard/activity');
    return response;
  }
}

export const dashboardService = new DashboardService();
export default dashboardService;
