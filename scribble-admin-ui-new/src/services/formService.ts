import { Form, FormTemplate } from '@/types';
import { apiService } from './api';

class FormService {
  async getForms() {
    const response = await apiService.get('/visit/form');
    return response;
  }

  async createForm(data: Partial<Form>) {
    const response = await apiService.post('/visit/form', data);
    return response;
  }

  async updateForm(id: string, data: Partial<Form>) {
    const response = await apiService.put(`/visit/form/${id}`, data);
    return response;
  }

  async deleteForm(id: string) {
    const response = await apiService.delete(`/visit/form/${id}`);
    return response;
  }

  async getFormById(id: string) {
    const response = await apiService.get(`/visit/form/${id}`);
    return response;
  }

  async getTemplates() {
    const response = await apiService.get('/visit/formtemplate');
    return response;
  }

  async createTemplate(data: Partial<FormTemplate>) {
    const response = await apiService.post('/visit/formtemplate', data);
    return response;
  }

  async updateTemplate(id: string, data: Partial<FormTemplate>) {
    const response = await apiService.put(`/visit/formtemplate/${id}`, data);
    return response;
  }

  async deleteTemplate(id: string) {
    const response = await apiService.delete(`/visit/formtemplate/${id}`);
    return response;
  }
}

export const formService = new FormService();
export default formService;
