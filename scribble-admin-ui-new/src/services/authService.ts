import { LoginCredentials, User } from '@/types';
import { apiService } from './api';

class AuthService {
  async login(credentials: LoginCredentials) {
    const response = await apiService.post('/auth/login', credentials);
    return response;
  }

  async logout() {
    const response = await apiService.post('/auth/logout');
    return response;
  }

  async refreshToken() {
    const refreshToken = localStorage.getItem('refreshToken');
    const response = await apiService.post('/auth/refresh', { refreshToken });
    return response;
  }

  async getCurrentUser() {
    const response = await apiService.get('/auth/me');
    return response;
  }

  async changePassword(data: { currentPassword: string; newPassword: string }) {
    const response = await apiService.put('/auth/change-password', data);
    return response;
  }

  async updateProfile(data: Partial<User>) {
    const response = await apiService.put('/auth/me', data);
    return response;
  }

  async sendPasswordResetEmail(email: string) {
    const response = await apiService.post('/auth/recover-password-email', { email });
    return response;
  }

  async resetPassword(data: { token: string; newPassword: string }) {
    const response = await apiService.post('/auth/recover-password', data);
    return response;
  }
}

export const authService = new AuthService();
export default authService;
