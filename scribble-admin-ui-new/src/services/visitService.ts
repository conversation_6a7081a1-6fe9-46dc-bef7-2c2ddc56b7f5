import { Visit } from '@/types';
import { apiService } from './api';

class VisitService {
  async getVisits() {
    const response = await apiService.get('/visit');
    return response;
  }

  async createVisit(data: Partial<Visit>) {
    const response = await apiService.post('/visit', data);
    return response;
  }

  async updateVisit(id: string, data: Partial<Visit>) {
    const response = await apiService.put(`/visit/${id}`, data);
    return response;
  }

  async deleteVisit(id: string) {
    const response = await apiService.delete(`/visit/${id}`);
    return response;
  }

  async getVisitById(id: string) {
    const response = await apiService.get(`/visit/${id}`);
    return response;
  }
}

export const visitService = new VisitService();
export default visitService;
