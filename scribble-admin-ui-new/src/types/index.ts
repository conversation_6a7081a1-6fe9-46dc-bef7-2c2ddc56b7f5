// User Types
export interface User {
  id: string;
  email: string;
  firstName?: string;
  lastName?: string;
  roleId: string;
  roleName?: string;
  isFirstLogin: boolean;
  lastLoginAt?: string;
  status: 'active' | 'pending' | 'suspended' | 'inactive' | 'deleted' | 'locked';
  createdAt: string;
  updatedAt: string;
}

// Role Types
export interface Role {
  id: string;
  name: string;
  permission: string[];
  createdAt: string;
  updatedAt: string;
}

// Tenant Types
export interface Tenant {
  id: string;
  tenantName: string;
  uniqueName: string;
  databaseName: string;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
}

// Visit Types
export interface Visit {
  id: string;
  clientId: string;
  clinicianId: string;
  visitDate: string;
  visitType: string;
  status: 'scheduled' | 'in-progress' | 'completed' | 'cancelled';
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

// Form Types
export interface FormTemplate {
  id: string;
  name: string;
  question: any[];
  disciplineId: string;
  formTypeId: string;
  createdAt: string;
  updatedAt: string;
}

export interface Form {
  id: string;
  templateId: string;
  visitId: string;
  answers: any[];
  status: 'draft' | 'completed' | 'submitted';
  createdAt: string;
  updatedAt: string;
}

// Auth Types
export interface AuthState {
  isAuthenticated: boolean;
  user: User | null;
  userType: 'super_admin' | 'tenant_admin' | 'clinician' | null;
  token: string | null;
  tenantId?: string;
}

export interface LoginCredentials {
  email: string;
  password: string;
  tenantId?: string;
}

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  data: T;
  message?: string;
  totalCount?: number;
}

export interface ApiError {
  message: string;
  errorCode?: string;
  details?: any;
}

// Dashboard Types
export interface DashboardStats {
  totalUsers: number;
  totalTenants: number;
  totalVisits: number;
  totalForms: number;
  recentActivity: ActivityItem[];
}

export interface ActivityItem {
  id: string;
  type: 'user_created' | 'visit_completed' | 'form_submitted' | 'tenant_created';
  description: string;
  timestamp: string;
  userId?: string;
  userName?: string;
}

// Table Types
export interface TableColumn {
  field: string;
  headerName: string;
  width?: number;
  sortable?: boolean;
  filterable?: boolean;
  renderCell?: (params: any) => React.ReactNode;
}

export interface TablePagination {
  page: number;
  pageSize: number;
  total: number;
}

// Notification Types
export interface Notification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  message: string;
  autoHide?: boolean;
  duration?: number;
}

// Filter Types
export interface FilterOptions {
  search?: string;
  status?: string;
  dateFrom?: string;
  dateTo?: string;
  roleId?: string;
  tenantId?: string;
}

// Form Validation Types
export interface ValidationError {
  field: string;
  message: string;
}

export interface FormState<T = any> {
  data: T;
  errors: ValidationError[];
  isSubmitting: boolean;
  isDirty: boolean;
}
